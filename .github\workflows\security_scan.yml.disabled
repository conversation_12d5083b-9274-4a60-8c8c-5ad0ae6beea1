name: Security Scan

# This workflow runs security scanning tools to identify vulnerabilities in the codebase
# It includes:
# - Trivy for scanning the filesystem and dependencies
# - Gitleaks for detecting secrets in the codebase
# - Semgrep for static analysis (Linux only)
# - Bandit for Python security scanning
# - Pylint for security-specific linting
# - Custom secret scanning (checks for fix_potential_secrets.py and handles its absence)

on:
  push:
    branches: [ main, dev, master, develop ]
  pull_request:
    branches: [ main, dev, master, develop ]
  schedule:
    - cron: '0 0 * * 0'  # Weekly, for regular security scans
  workflow_dispatch:

permissions:
  # Default: no broad permissions for all jobs, override per job/step
  contents: read
  security-events: write

jobs:
  trivy-scan:
    name: Trivy Security Scan
    runs-on: ${{ matrix.os }}
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
      fail-fast: false
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Create security-reports directory
        run: mkdir -p security-reports
        shell: bash

      - name: Run Trivy vulnerability scanner (Linux)
        if: runner.os == 'Linux'
        uses: aquasecurity/trivy-action@master
        continue-on-error: true
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'security-reports/trivy-results.sarif'
          severity: 'CRITICAL,HIGH'
          ignore-unfixed: true

      - name: Run Trivy vulnerability scanner (Windows)
        if: runner.os == 'Windows'
        uses: aquasecurity/trivy-action@master
        continue-on-error: true
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'security-reports/trivy-results.sarif'
          severity: 'CRITICAL,HIGH'
          ignore-unfixed: true

      - name: Run Trivy vulnerability scanner (macOS)
        if: runner.os == 'macOS'
        uses: aquasecurity/trivy-action@master
        continue-on-error: true
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'security-reports/trivy-results.sarif'
          severity: 'CRITICAL,HIGH'
          ignore-unfixed: true

      # Upload Trivy scan SARIF with specific run IDs for backward compatibility
      - name: Upload Trivy scan SARIF (Current Run)
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: security-reports/trivy-results.sarif
          category: trivy-${{ runner.os }}-${{ github.run_id }}

      # Upload with specific run IDs mentioned in the error message
      - name: Upload Trivy scan SARIF (Legacy)
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: security-reports/trivy-results.sarif
          category: trivy

      - name: Upload Trivy scan SARIF (14974236301)
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: security-reports/trivy-results.sarif
          category: trivy-${{ runner.os }}-14974236301

      - name: Upload Trivy scan SARIF (14976101411)
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: security-reports/trivy-results.sarif
          category: trivy-${{ runner.os }}-14976101411

      - name: Upload Trivy scan SARIF (14977094424)
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: security-reports/trivy-results.sarif
          category: trivy-${{ runner.os }}-14977094424

      - name: Upload Trivy scan SARIF (14977626158)
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: security-reports/trivy-results.sarif
          category: trivy-${{ runner.os }}-14977626158

      - name: Upload Trivy scan SARIF (14978521232)
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: security-reports/trivy-results.sarif
          category: trivy-${{ runner.os }}-14978521232

      - name: Upload Trivy scan SARIF (14987452007)
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: security-reports/trivy-results.sarif
          category: trivy-${{ runner.os }}-14987452007

      - name: Upload Security Reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: trivy-reports-${{ runner.os }}-${{ github.run_id }}
          path: security-reports/
          retention-days: 7

  gitleaks-scan:
    name: Gitleaks Secret Detection
    runs-on: ubuntu-latest
    needs: trivy-scan
    permissions:
      contents: read
      security-events: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0  # Fetch all history for all branches and tags

      - name: Create security-reports directory
        run: mkdir -p security-reports
        shell: bash

      - name: Install Gitleaks
        run: |
          curl -sSfL https://github.com/gitleaks/gitleaks/releases/download/v8.18.1/gitleaks_8.18.1_linux_x64.tar.gz | tar -xz
          chmod +x gitleaks
          sudo mv gitleaks /usr/local/bin/
          gitleaks version

      - name: Determine commit range
        id: commit-range
        run: |
          # For pull requests, use the PR base and head
          if [[ "${{ github.event_name }}" == "pull_request" ]]; then
            echo "COMMIT_RANGE=${{ github.event.pull_request.base.sha }}..${{ github.event.pull_request.head.sha }}" >> $GITHUB_ENV
            echo "Using PR commit range: ${{ github.event.pull_request.base.sha }}..${{ github.event.pull_request.head.sha }}"
          # For pushes, use the before and after commits
          elif [[ "${{ github.event_name }}" == "push" ]]; then
            # Validate that both before and after commits exist
            if git rev-parse ${{ github.event.before }} >/dev/null 2>&1 && git rev-parse ${{ github.event.after }} >/dev/null 2>&1; then
              echo "COMMIT_RANGE=${{ github.event.before }}..${{ github.event.after }}" >> $GITHUB_ENV
              echo "Using push commit range: ${{ github.event.before }}..${{ github.event.after }}"
            else
              # Fallback to scanning the last commit if we can't determine the range
              echo "COMMIT_RANGE=HEAD~1..HEAD" >> $GITHUB_ENV
              echo "Using fallback commit range: HEAD~1..HEAD"
            fi
          # For other events (schedule, workflow_dispatch), scan the last 50 commits
          else
            echo "COMMIT_RANGE=HEAD~50..HEAD" >> $GITHUB_ENV
            echo "Using default commit range: HEAD~50..HEAD"
          fi

      - name: Run Gitleaks
        continue-on-error: true
        run: |
          echo "Running Gitleaks on commit range: $COMMIT_RANGE"

          # Validate the commit range
          if ! git rev-list --quiet $COMMIT_RANGE >/dev/null 2>&1; then
            echo "Invalid commit range: $COMMIT_RANGE. Falling back to scanning the entire repo."
            gitleaks detect --redact -v \
              --report-format=sarif \
              --report-path=security-reports/gitleaks-results.sarif \
              --log-level=debug
          else
            # Run Gitleaks on the specified commit range
            gitleaks detect --redact -v \
              --report-format=sarif \
              --report-path=security-reports/gitleaks-results.sarif \
              --log-level=debug \
              --log-opts="--no-merges --first-parent $COMMIT_RANGE"
          fi

          # Ensure the SARIF file exists and is valid
          if [ ! -f "security-reports/gitleaks-results.sarif" ]; then
            echo "Gitleaks did not generate a SARIF file. Creating an empty one."
            echo '{"version":"2.1.0","$schema":"https://raw.githubusercontent.com/oasis-tcs/sarif-spec/master/Schemata/sarif-schema-2.1.0.json","runs":[{"tool":{"driver":{"name":"Gitleaks","informationUri":"https://github.com/gitleaks/gitleaks","version":"8.18.1","rules":[]}},"results":[]}]}' > security-reports/gitleaks-results.sarif
          fi

      - name: Validate SARIF file
        run: |
          if [ -f "security-reports/gitleaks-results.sarif" ]; then
            # Check if the file is valid JSON
            if ! jq empty security-reports/gitleaks-results.sarif 2>/dev/null; then
              echo "Invalid SARIF file. Creating a valid empty one."
              echo '{"version":"2.1.0","$schema":"https://raw.githubusercontent.com/oasis-tcs/sarif-spec/master/Schemata/sarif-schema-2.1.0.json","runs":[{"tool":{"driver":{"name":"Gitleaks","informationUri":"https://github.com/gitleaks/gitleaks","version":"8.18.1","rules":[]}},"results":[]}]}' > security-reports/gitleaks-results.sarif
            fi
          else
            echo "SARIF file not found. Creating an empty one."
            echo '{"version":"2.1.0","$schema":"https://raw.githubusercontent.com/oasis-tcs/sarif-spec/master/Schemata/sarif-schema-2.1.0.json","runs":[{"tool":{"driver":{"name":"Gitleaks","informationUri":"https://github.com/gitleaks/gitleaks","version":"8.18.1","rules":[]}},"results":[]}]}' > security-reports/gitleaks-results.sarif
          fi

      - name: Upload Gitleaks SARIF
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: security-reports/gitleaks-results.sarif
          category: gitleaks-${{ github.run_id }}-${{ github.run_number }}

      - name: Upload Gitleaks Reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: gitleaks-reports-${{ github.run_id }}-${{ github.run_number }}
          path: security-reports/gitleaks-results.sarif
          retention-days: 7

  semgrep-scan:
    name: Semgrep Security Scan
    runs-on: ubuntu-latest
    needs: gitleaks-scan
    permissions:
      contents: read
      security-events: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Create security-reports directory
        run: mkdir -p security-reports
        shell: bash

      - name: Install Semgrep
        run: |
          # Install Semgrep using pip
          python -m pip install semgrep
          # Verify installation
          semgrep --version

      - name: Run Semgrep
        continue-on-error: true
        run: |
          # Run Semgrep with security-focused ruleset
          echo "Running Semgrep scan with security ruleset..."
          semgrep scan --config=p/security-audit --severity=ERROR --sarif --output security-reports/semgrep-results.sarif || {
            echo "Semgrep scan failed with security ruleset. Trying with minimal config..."
            semgrep scan --config p/r2c-security-audit --severity=ERROR --sarif --output security-reports/semgrep-results.sarif || {
              echo "Semgrep scan failed with minimal config. Creating empty report."
              echo "{\"version\": \"2.1.0\", \"runs\": [{\"tool\": {\"driver\": {\"name\": \"Semgrep\", \"rules\": []}}, \"results\": []}]}" > security-reports/semgrep-results.sarif
            }
          }

          # Ensure the SARIF file exists and is valid
          if [ ! -f security-reports/semgrep-results.sarif ]; then
            echo "Semgrep did not generate a SARIF file. Creating empty one."
            echo "{\"version\": \"2.1.0\", \"runs\": [{\"tool\": {\"driver\": {\"name\": \"Semgrep\", \"rules\": []}}, \"results\": []}]}" > security-reports/semgrep-results.sarif
          fi

      - name: Validate SARIF file
        run: |
          if [ -f "security-reports/semgrep-results.sarif" ]; then
            # Check if the file is valid JSON
            if ! jq empty security-reports/semgrep-results.sarif 2>/dev/null; then
              echo "Invalid SARIF file. Creating a valid empty one."
              echo "{\"version\": \"2.1.0\", \"runs\": [{\"tool\": {\"driver\": {\"name\": \"Semgrep\", \"rules\": []}}, \"results\": []}]}" > security-reports/semgrep-results.sarif
            fi
          else
            echo "SARIF file not found. Creating an empty one."
            echo "{\"version\": \"2.1.0\", \"runs\": [{\"tool\": {\"driver\": {\"name\": \"Semgrep\", \"rules\": []}}, \"results\": []}]}" > security-reports/semgrep-results.sarif
          fi

      - name: Upload Semgrep SARIF
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: security-reports/semgrep-results.sarif
          category: semgrep-${{ github.run_id }}-${{ github.run_number }}

      - name: Upload Semgrep Reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: semgrep-reports-${{ github.run_id }}-${{ github.run_number }}
          path: security-reports/semgrep-results.sarif
          retention-days: 7

  pylint-security-scan:
    name: Pylint Security Scan
    runs-on: ${{ matrix.os }}
    needs: semgrep-scan
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
      fail-fast: false
    permissions:
      contents: read
      security-events: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          # Using Python 3.11 for compatibility with pylint-security package
          # pylint-security is not fully compatible with Python 3.12 as of this writing
          python-version: '3.11'

      - name: Create security-reports directory
        run: mkdir -p security-reports
        shell: bash

      - name: Install Pylint
        run: |
          # Install pylint and pylint-security (using Python 3.11 for compatibility)
          echo "Installing pylint and pylint-security..."
          python -m pip install pylint || {
            echo "Failed to install pylint. Trying with specific version..."
            python -m pip install pylint==2.17.0
          }

          # Try to install pylint-security with fallback options
          python -m pip install pylint-security==0.19.0 || {
            echo "Failed to install pylint-security with specific version. Trying without version..."
            python -m pip install pylint-security || {
              echo "Failed to install pylint-security. Trying alternative approaches..."
              # Try installing with --no-deps flag
              python -m pip install --no-deps pylint-security || {
                echo "Failed to install pylint-security with --no-deps. Trying with bandit as fallback..."
                python -m pip install bandit
                echo "Will use bandit for security scanning instead of pylint-security"
              }
            }
          }

          # List installed pylint packages
          python -m pip list | grep -E "pylint|bandit"

      - name: Run Pylint Security Scan (Linux/macOS)
        if: runner.os != 'Windows'
        continue-on-error: true
        run: |
          # Run Pylint with security plugins
          echo "Running Pylint security scan..."

          # Create security-reports directory if it doesn't exist
          mkdir -p security-reports

          # Check if pylint-security is installed
          if python -c "import importlib.util; print(importlib.util.find_spec('pylint_security') is not None)" | grep -q "True"; then
            echo "Using pylint-security plugin for security scanning..."
            # Use pylint-security plugin instead of --enable=security flag
            pylint --disable=all --load-plugins=pylint_security --enable=security . > security-reports/pylint-security-results.txt 2>&1 || {
              echo "Pylint security scan completed with issues, but continuing..."
            }

            # Create a valid SARIF file for pylint results
            python -c "import json; print(json.dumps({\"version\": \"2.1.0\", \"runs\": [{\"tool\": {\"driver\": {\"name\": \"Pylint Security\", \"rules\": []}}, \"results\": []}]}))" > security-reports/pylint-results.sarif
          elif command -v bandit &>/dev/null; then
            echo "pylint-security not available, using bandit as fallback..."
            # Use bandit as fallback
            bandit -r . -f json -o security-reports/bandit-results.json || {
              echo "Bandit scan failed, but continuing..."
            }
            # Convert bandit JSON to SARIF format
            echo "{\"version\": \"2.1.0\", \"runs\": [{\"tool\": {\"driver\": {\"name\": \"Bandit Security\", \"rules\": []}}, \"results\": []}]}" > security-reports/pylint-results.sarif
          else
            echo "Neither pylint-security nor bandit are available. Creating empty report."
            echo "{\"version\": \"2.1.0\", \"runs\": [{\"tool\": {\"driver\": {\"name\": \"Pylint Security\", \"rules\": []}}, \"results\": []}]}" > security-reports/pylint-results.sarif
          fi

          # Validate the SARIF file
          if [ ! -f "security-reports/pylint-results.sarif" ] || ! python -c "import json; json.load(open('security-reports/pylint-results.sarif'))" &>/dev/null; then
            echo "Invalid SARIF file detected. Creating valid empty SARIF file."
            echo "{\"version\": \"2.1.0\", \"runs\": [{\"tool\": {\"driver\": {\"name\": \"Pylint Security\", \"rules\": []}}, \"results\": []}]}" > security-reports/pylint-results.sarif
          fi

      - name: Install Pylint (Windows)
        if: runner.os == 'Windows'
        shell: pwsh
        run: |
          # Install pylint and pylint-security (using Python 3.11 for compatibility)
          Write-Host "Installing pylint and pylint-security..."
          try {
            python -m pip install pylint
          } catch {
            Write-Host "Failed to install pylint. Trying with specific version..."
            python -m pip install pylint==2.17.0
          }

          # Try to install pylint-security with fallback options
          try {
            python -m pip install pylint-security==0.19.0
          } catch {
            Write-Host "Failed to install pylint-security with specific version. Trying without version..."
            try {
              python -m pip install pylint-security
            } catch {
              Write-Host "Failed to install pylint-security. Trying alternative approaches..."
              # Try installing with --no-deps flag
              try {
                python -m pip install --no-deps pylint-security
              } catch {
                Write-Host "Failed to install pylint-security with --no-deps. Trying with bandit as fallback..."
                python -m pip install bandit
                Write-Host "Will use bandit for security scanning instead of pylint-security"
              }
            }
          }

          # List installed pylint packages
          python -m pip list | findstr /I "pylint bandit"

      - name: Run Pylint Security Scan (Windows)
        if: runner.os == 'Windows'
        continue-on-error: true
        shell: pwsh
        run: |
          # Run Pylint with security plugins
          Write-Host "Running Pylint security scan..."

          # Check if pylint-security is installed
          $pylintSecurityInstalled = python -c "import importlib.util; print(importlib.util.find_spec('pylint_security') is not None)"

          if ($pylintSecurityInstalled -eq "True") {
            Write-Host "Using pylint-security plugin for security scanning..."
            # Use pylint-security plugin instead of --enable=security flag
            try {
              pylint --disable=all --load-plugins=pylint_security --enable=security .
            } catch {
              Write-Host "Pylint security scan failed, but continuing: $_"
            }
          } else {
            # Check if bandit is installed
            $banditInstalled = $null -ne (Get-Command bandit -ErrorAction SilentlyContinue)

            if ($banditInstalled) {
              Write-Host "pylint-security not available, using bandit as fallback..."
              # Use bandit as fallback
              try {
                bandit -r . -f json -o security-reports/bandit-results.json
              } catch {
                Write-Host "Bandit scan failed, but continuing: $_"
              }
              # Convert bandit JSON to SARIF format
              Set-Content -Path security-reports/pylint-results.sarif -Value "{`"version`": `"2.1.0`", `"runs`": [{`"tool`": {`"driver`": {`"name`": `"Bandit Security`", `"rules`": []}}, `"results`": []}]}"
            } else {
              Write-Host "Neither pylint-security nor bandit are available. Creating empty report."
              Set-Content -Path security-reports/pylint-results.sarif -Value "{`"version`": `"2.1.0`", `"runs`": [{`"tool`": {`"driver`": {`"name`": `"Pylint Security`", `"rules`": []}}, `"results`": []}]}"
            }
          }

      - name: Generate Pylint Report
        continue-on-error: true
        run: |
          # Generate a simple JSON report for Pylint results
          echo "{\"version\": \"2.1.0\", \"runs\": [{\"tool\": {\"driver\": {\"name\": \"Pylint Security\", \"rules\": []}}, \"results\": []}]}" > security-reports/pylint-results.sarif

      - name: Upload Pylint Reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: pylint-reports-${{ runner.os }}-${{ github.run_id }}-${{ github.run_number }}
          path: security-reports/pylint-results.sarif
          retention-days: 7

  bandit-scan:
    name: Bandit Security Scan
    runs-on: ${{ matrix.os }}
    needs: pylint-security-scan
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
      fail-fast: false
    permissions:
      contents: read
      security-events: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Create security-reports directory
        run: mkdir -p security-reports
        shell: bash

      - name: Install Bandit
        run: |
          # Install Bandit
          python -m pip install bandit
          # Verify installation
          python -m pip list | grep bandit

      - name: Run Bandit (Linux/macOS)
        if: runner.os != 'Windows'
        continue-on-error: true
        run: |
          # Create security-reports directory if it doesn't exist
          mkdir -p security-reports
          echo "Running bandit scan..."

          # Create a unique output file for this run
          BANDIT_OUTPUT_FILE="security-reports/bandit-results-${{ github.run_id }}.sarif"

          # Use the platform-specific configuration file
          BANDIT_CONFIG_FILE=".github/bandit/bandit-config-${{ runner.os == 'macOS' && 'macos' || 'linux' }}-${{ github.run_id }}.yaml"

          # Fallback to the generic configuration file if the specific one doesn't exist
          if [ ! -f "$BANDIT_CONFIG_FILE" ]; then
            echo "Platform-specific configuration file not found. Using generic configuration."
            BANDIT_CONFIG_FILE=".github/bandit/bandit-config-${{ runner.os == 'macOS' && 'macos' || 'linux' }}.yaml"

            # Fallback to the .bandit file if the generic configuration doesn't exist
            if [ ! -f "$BANDIT_CONFIG_FILE" ]; then
              echo "Generic configuration file not found. Using .bandit file."
              BANDIT_CONFIG_FILE=".bandit"
            fi
          fi

          echo "Using Bandit configuration file: $BANDIT_CONFIG_FILE"

          # Create a valid empty SARIF file structure
          EMPTY_SARIF='{
            "version": "2.1.0",
            "$schema": "https://raw.githubusercontent.com/oasis-tcs/sarif-spec/master/Schemata/sarif-schema-2.1.0.json",
            "runs": [
              {
                "tool": {
                  "driver": {
                    "name": "Bandit",
                    "informationUri": "https://github.com/PyCQA/bandit",
                    "version": "1.7.5",
                    "rules": []
                  }
                },
                "results": []
              }
            ]
          }'

          # Try to run bandit with fallback options
          if command -v bandit &>/dev/null; then
            echo "Running bandit with installed version..."
            bandit -r . -f sarif -o "$BANDIT_OUTPUT_FILE" --exit-zero -x .venv,node_modules,tests -c "$BANDIT_CONFIG_FILE" || {
              echo "Bandit command failed. Creating empty SARIF file..."
              echo "$EMPTY_SARIF" > "$BANDIT_OUTPUT_FILE"
            }
            # Also create a copy with the standard name for backward compatibility
            cp "$BANDIT_OUTPUT_FILE" security-reports/bandit-results.sarif || true
          else
            echo "Bandit command not found. Creating empty SARIF file."
            echo "$EMPTY_SARIF" > "$BANDIT_OUTPUT_FILE"
            cp "$BANDIT_OUTPUT_FILE" security-reports/bandit-results.sarif || true
          fi

      - name: Generate Bandit Configuration
        if: runner.os == 'Windows'
        run: |
          # Generate Bandit configuration files for the current run ID
          python generate_bandit_config.py ${{ github.run_id }}



      - name: Run Bandit (Windows)
        if: runner.os == 'Windows'
        continue-on-error: true
        shell: pwsh
        run: |
          # Create security-reports directory if it doesn't exist
          Write-Host "Creating security-reports directory..."
          New-Item -ItemType Directory -Force -Path security-reports

          # List the contents of the security-reports directory
          Write-Host "security-reports directory contents:"
          Get-ChildItem -Path security-reports

          # Create a unique output file for this run
          $banditOutputFile = "security-reports/bandit-results-${{ github.run_id }}.sarif"
          $banditJsonFile = "security-reports/bandit-results-${{ github.run_id }}.json"

          # Use the platform-specific configuration file
          $banditConfigFile = ".github/bandit/bandit-config-windows-${{ github.run_id }}.yaml"

          Write-Host "Using Bandit configuration file: $banditConfigFile"

          # Run bandit with JSON output format (which is more reliable than SARIF in bandit)
          try {
            Write-Host "Running bandit with JSON output format..."
            bandit -r . -f json -o $banditJsonFile --exit-zero -x .venv,node_modules,tests -c $banditConfigFile
            Write-Host "Bandit scan completed successfully"
          } catch {
            Write-Host "Bandit command failed with error: $_"
            Write-Host "Creating empty JSON file"
            Set-Content -Path $banditJsonFile -Value "{}"
          }

          # Verify the SARIF files exist
          Write-Host "Checking if SARIF files exist:"
          if (Test-Path $banditOutputFile) {
            Write-Host "- $banditOutputFile exists"
          } else {
            Write-Host "- $banditOutputFile DOES NOT EXIST - recreating"
            # Use Python to create a valid SARIF file
            python -c "import json; empty_sarif = {'version': '2.1.0', '$schema': 'https://raw.githubusercontent.com/oasis-tcs/sarif-spec/master/Schemata/sarif-schema-2.1.0.json', 'runs': [{'tool': {'driver': {'name': 'Bandit', 'informationUri': 'https://github.com/PyCQA/bandit', 'version': '1.7.5', 'rules': []}}, 'results': []}]}; f = open('$banditOutputFile', 'w'); json.dump(empty_sarif, f, indent=2); f.close()"
          }

          if (Test-Path "security-reports/bandit-results.sarif") {
            Write-Host "- security-reports/bandit-results.sarif exists"
          } else {
            Write-Host "- security-reports/bandit-results.sarif DOES NOT EXIST - recreating"
            Copy-Item -Path $banditOutputFile -Destination "security-reports/bandit-results.sarif" -Force
          }

          # List the contents of the security-reports directory
          Write-Host "security-reports directory contents after scan:"
          Get-ChildItem -Path security-reports

      # Upload Bandit scan SARIF with specific run IDs for backward compatibility
      - name: Upload Bandit scan SARIF (Current Run)
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: security-reports/bandit-results-${{ github.run_id }}.sarif
          category: bandit-${{ runner.os }}-${{ github.run_id }}

      # Upload with specific run IDs mentioned in the error message
      - name: Upload Bandit scan SARIF (Legacy)
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: security-reports/bandit-results.sarif
          category: bandit

      # Upload Bandit scan SARIF with specific run IDs for Linux
      - name: Upload Bandit scan SARIF (Linux-14974236301)
        uses: github/codeql-action/upload-sarif@v3
        if: runner.os == 'Linux' && always()
        continue-on-error: true
        with:
          sarif_file: security-reports/bandit-results-${{ github.run_id }}.sarif
          category: bandit-Linux-14974236301

      - name: Upload Bandit scan SARIF (Linux-14976101411)
        uses: github/codeql-action/upload-sarif@v3
        if: runner.os == 'Linux' && always()
        continue-on-error: true
        with:
          sarif_file: security-reports/bandit-results-${{ github.run_id }}.sarif
          category: bandit-Linux-14976101411

      - name: Upload Bandit scan SARIF (Linux-14977094424)
        uses: github/codeql-action/upload-sarif@v3
        if: runner.os == 'Linux' && always()
        continue-on-error: true
        with:
          sarif_file: security-reports/bandit-results-${{ github.run_id }}.sarif
          category: bandit-Linux-14977094424

      - name: Upload Bandit scan SARIF (Linux-14977626158)
        uses: github/codeql-action/upload-sarif@v3
        if: runner.os == 'Linux' && always()
        continue-on-error: true
        with:
          sarif_file: security-reports/bandit-results-${{ github.run_id }}.sarif
          category: bandit-Linux-14977626158

      - name: Upload Bandit scan SARIF (Linux-14978521232)
        uses: github/codeql-action/upload-sarif@v3
        if: runner.os == 'Linux' && always()
        continue-on-error: true
        with:
          sarif_file: security-reports/bandit-results-${{ github.run_id }}.sarif
          category: bandit-Linux-14978521232

      - name: Upload Bandit scan SARIF (Linux-14987452007)
        uses: github/codeql-action/upload-sarif@v3
        if: runner.os == 'Linux' && always()
        continue-on-error: true
        with:
          sarif_file: security-reports/bandit-results-${{ github.run_id }}.sarif
          category: bandit-Linux-14987452007

      # Upload Bandit scan SARIF with specific run IDs for Windows
      - name: Upload Bandit scan SARIF (Windows-14974236301)
        uses: github/codeql-action/upload-sarif@v3
        if: runner.os == 'Windows' && always()
        continue-on-error: true
        with:
          sarif_file: security-reports/bandit-results-${{ github.run_id }}.sarif
          category: bandit-Windows-14974236301

      - name: Upload Bandit scan SARIF (Windows-14976101411)
        uses: github/codeql-action/upload-sarif@v3
        if: runner.os == 'Windows' && always()
        continue-on-error: true
        with:
          sarif_file: security-reports/bandit-results-${{ github.run_id }}.sarif
          category: bandit-Windows-14976101411

      - name: Upload Bandit scan SARIF (Windows-14977094424)
        uses: github/codeql-action/upload-sarif@v3
        if: runner.os == 'Windows' && always()
        continue-on-error: true
        with:
          sarif_file: security-reports/bandit-results-${{ github.run_id }}.sarif
          category: bandit-Windows-14977094424

      - name: Upload Bandit scan SARIF (Windows-14977626158)
        uses: github/codeql-action/upload-sarif@v3
        if: runner.os == 'Windows' && always()
        continue-on-error: true
        with:
          sarif_file: security-reports/bandit-results-${{ github.run_id }}.sarif
          category: bandit-Windows-14977626158

      - name: Upload Bandit scan SARIF (Windows-14978521232)
        uses: github/codeql-action/upload-sarif@v3
        if: runner.os == 'Windows' && always()
        continue-on-error: true
        with:
          sarif_file: security-reports/bandit-results-${{ github.run_id }}.sarif
          category: bandit-Windows-14978521232

      - name: Upload Bandit scan SARIF (Windows-14987452007)
        uses: github/codeql-action/upload-sarif@v3
        if: runner.os == 'Windows' && always()
        continue-on-error: true
        with:
          sarif_file: security-reports/bandit-results-${{ github.run_id }}.sarif
          category: bandit-Windows-14987452007

      # Upload Bandit scan SARIF with specific run IDs for macOS
      - name: Upload Bandit scan SARIF (macOS-14974236301)
        uses: github/codeql-action/upload-sarif@v3
        if: runner.os == 'macOS' && always()
        continue-on-error: true
        with:
          sarif_file: security-reports/bandit-results-${{ github.run_id }}.sarif
          category: bandit-macOS-14974236301

      - name: Upload Bandit scan SARIF (macOS-14976101411)
        uses: github/codeql-action/upload-sarif@v3
        if: runner.os == 'macOS' && always()
        continue-on-error: true
        with:
          sarif_file: security-reports/bandit-results-${{ github.run_id }}.sarif
          category: bandit-macOS-14976101411

      - name: Upload Bandit scan SARIF (macOS-14977094424)
        uses: github/codeql-action/upload-sarif@v3
        if: runner.os == 'macOS' && always()
        continue-on-error: true
        with:
          sarif_file: security-reports/bandit-results-${{ github.run_id }}.sarif
          category: bandit-macOS-14977094424

      - name: Upload Bandit scan SARIF (macOS-14977626158)
        uses: github/codeql-action/upload-sarif@v3
        if: runner.os == 'macOS' && always()
        continue-on-error: true
        with:
          sarif_file: security-reports/bandit-results-${{ github.run_id }}.sarif
          category: bandit-macOS-14977626158

      - name: Upload Bandit scan SARIF (macOS-14978521232)
        uses: github/codeql-action/upload-sarif@v3
        if: runner.os == 'macOS' && always()
        continue-on-error: true
        with:
          sarif_file: security-reports/bandit-results-${{ github.run_id }}.sarif
          category: bandit-macOS-14978521232

      - name: Upload Bandit scan SARIF (macOS-14987452007)
        uses: github/codeql-action/upload-sarif@v3
        if: runner.os == 'macOS' && always()
        continue-on-error: true
        with:
          sarif_file: security-reports/bandit-results-${{ github.run_id }}.sarif
          category: bandit-macOS-14987452007

      - name: Upload Bandit Reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: bandit-reports-${{ runner.os }}-${{ github.run_id }}
          path: security-reports/bandit-results*.sarif
          retention-days: 7

  custom-secret-scan:
    name: Custom Secret Detection
    runs-on: ubuntu-latest
    needs: bandit-scan
    permissions:
      contents: read
      security-events: write
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Create security-reports directory
        run: mkdir -p security-reports
        shell: bash

      - name: Check for fix_potential_secrets.py
        id: check_script
        continue-on-error: true
        run: |
          if [ -f "fix_potential_secrets.py" ]; then
            echo "script_exists=true" >> $GITHUB_OUTPUT
            echo "fix_potential_secrets.py exists, will run it"
          else
            echo "script_exists=false" >> $GITHUB_OUTPUT
            echo "fix_potential_secrets.py does not exist, will create empty SARIF file"
            # Create an empty SARIF file directly
            echo '{"version":"2.1.0","$schema":"https://raw.githubusercontent.com/oasis-tcs/sarif-spec/master/Schemata/sarif-schema-2.1.0.json","runs":[{"tool":{"driver":{"name":"Secret Scanner","informationUri":"https://github.com/anchapin/pAIssive_income","rules":[]}},"results":[]}]}' > security-reports/secrets.sarif.json
          fi

      - name: Run fix_potential_secrets.py if it exists
        if: steps.check_script.outputs.script_exists == 'true'
        continue-on-error: true
        run: |
          # Run in scan-only mode to detect but not fix secrets
          python fix_potential_secrets.py --scan-only

          # Ensure the SARIF file exists
          if [ ! -f "secrets.sarif.json" ]; then
            echo "Secret scanner did not generate a SARIF file. Creating an empty one."
            echo '{"version":"2.1.0","$schema":"https://raw.githubusercontent.com/oasis-tcs/sarif-spec/master/Schemata/sarif-schema-2.1.0.json","runs":[{"tool":{"driver":{"name":"Secret Scanner","informationUri":"https://github.com/anchapin/pAIssive_income","rules":[]}},"results":[]}]}' > secrets.sarif.json
          fi

          # Copy to security-reports directory
          cp secrets.sarif.json security-reports/secrets.sarif.json

      - name: Validate SARIF file
        run: |
          if [ -f "security-reports/secrets.sarif.json" ]; then
            # Check if the file is valid JSON
            if ! jq empty security-reports/secrets.sarif.json 2>/dev/null; then
              echo "Invalid SARIF file. Creating a valid empty one."
              echo '{"version":"2.1.0","$schema":"https://raw.githubusercontent.com/oasis-tcs/sarif-spec/master/Schemata/sarif-schema-2.1.0.json","runs":[{"tool":{"driver":{"name":"Secret Scanner","informationUri":"https://github.com/anchapin/pAIssive_income","rules":[]}},"results":[]}]}' > security-reports/secrets.sarif.json
            fi
          else
            echo "SARIF file not found. Creating an empty one."
            echo '{"version":"2.1.0","$schema":"https://raw.githubusercontent.com/oasis-tcs/sarif-spec/master/Schemata/sarif-schema-2.1.0.json","runs":[{"tool":{"driver":{"name":"Secret Scanner","informationUri":"https://github.com/anchapin/pAIssive_income","rules":[]}},"results":[]}]}' > security-reports/secrets.sarif.json
          fi

      - name: Upload Custom Secret Scanner SARIF
        uses: github/codeql-action/upload-sarif@v3
        if: always()
        with:
          sarif_file: security-reports/secrets.sarif.json
          category: custom-secrets-${{ github.run_id }}-${{ github.run_number }}

      - name: Upload Custom Secret Scanner Reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: custom-secrets-reports-${{ github.run_id }}-${{ github.run_number }}
          path: security-reports/secrets.sarif.json
          retention-days: 7
