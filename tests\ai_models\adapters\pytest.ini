[pytest]
# Only collect tests from this directory
testpaths = .
pythonpath = ../../../
python_files = test_*.py
python_classes = Test*
python_functions = test_*

# Configure coverage for MCP tests
addopts = -v --cov=ai_models.adapters.mcp_adapter --cov=ai_models.adapters.exceptions --cov-report=xml --cov-report=term --cov-fail-under=80

# Disable parent conftest.py loading
noconftest = True
confcutdir = .

# Configure pytest-asyncio
asyncio_mode = auto
asyncio_default_fixture_loop_scope = function

# Filter common warnings
filterwarnings =
    ignore::ImportWarning
    ignore::DeprecationWarning

# Define all markers to prevent warnings
markers =
    unit: mark a test as a unit test
    integration: mark a test as an integration test
    slow: mark test as slow (taking more than 1 second to run)
    smoke: mark test as a smoke test (critical functionality)
    webhook: mark test related to webhook functionality
    api: mark test related to API functionality
    payment: mark test related to payment functionality
    security: mark test related to security features
    model: mark test related to AI model functionality
    performance: mark test performance-sensitive test
    flaky: mark test as flaky (occasionally fails)
    dependency: mark test as requiring external dependencies
    asyncio: mark test as an async test
    mcp: tests for the MCP adapter

# Configure test timeouts
timeout = 300
timeout_method = thread

# Logging configuration
log_cli = true
log_cli_level = INFO
log_cli_format = %(asctime)s [%(levelname)8s] %(message)s (%(filename)s:%(lineno)s)
log_cli_date_format = %Y-%m-%d %H:%M:%S
