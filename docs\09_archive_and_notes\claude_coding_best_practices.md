# Claude Coding Best Practices

Historical notes and best practices from <PERSON>.

---

## Archived Fix & Workflow Documentation

For deep-dive reference and audit history, the following files have been archived:

- [github_actions_fixes_summary.md](../../github_actions_fixes_summary.md)
- [github_actions_consolidation.md](../../github_actions_consolidation.md)
- [github_actions_local_testing.md](../../github_actions_local_testing.md)
- [github_actions_workflow_changes.md](../../github_actions_workflow_changes.md)
- [github_actions_workflow_optimization.md](../../github_actions_workflow_optimization.md)
- [MCP_ADAPTER_FIX.md](../../MCP_ADAPTER_FIX.md)
- [MCP_WORKFLOW_FIX.md](../../MCP_WORKFLOW_FIX.md)
- [security_fixes.md](../../security_fixes.md)
- [security_fixes_summary.md](../../security_fixes_summary.md)
- [codeql_fix_summary.md](../../codeql_fix_summary.md)
- [security_scan_readme.md](../../security_scan_readme.md)
- [syntax_fix_readme.md](../../syntax_fix_readme.md)
- [test_status_report.md](../../test_status_report.md)
- [improvement_plan.md](../../improvement_plan.md)
- [devops_tasks_status.md](../../devops_tasks_status.md)
- [update_github_actions_progress.md](../../update_github_actions_progress.md)
- [workflow_fixes_summary.md](../../workflow_fixes_summary.md)

These files contain granular fix notes, workflow history, and temporary documentation. Core actionable content has been merged into the main docs where relevant.