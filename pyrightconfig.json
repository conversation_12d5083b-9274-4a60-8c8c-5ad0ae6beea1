{"include": ["."], "exclude": ["**/__pycache__", "**/.git", "**/.github", "**/.venv", "**/venv", "**/node_modules", "**/build", "**/dist", "**/coverage", "**/playwright-report", "**/security-reports", "ai_models/adapters/mcp_adapter.py", "tests/ai_models/adapters/test_mcp_adapter.py", "tests/test_mcp_import.py", "tests/test_mcp_top_level_import.py", "tests/test_crewai_agents.py", "mock_mcp/**", "mock_crewai/**", "install_mcp_sdk.py", "run_mcp_tests.py", "run_crewai_tests.py", "ui/react_frontend/**", "scripts/__init__.py", ".github/scripts/__init__.py", ".github/scripts/**"], "ignore": ["migrations/env.py", "users/password_reset.py", "api/utils/auth.py", "test_sarif_utils.py", "scripts/manage_quality.py", "tests/api/**", "run_ui.py"], "defineConstant": {"DEBUG": true}, "stubPath": "mypy_stubs", "venvPath": ".", "venv": ".venv", "pythonVersion": "3.12", "pythonPlatform": "All", "executionEnvironments": [{"root": ".", "pythonVersion": "3.12", "pythonPlatform": "All", "extraPaths": [".", "mock_mcp", "mock_crewai"]}], "typeCheckingMode": "basic", "useLibraryCodeForTypes": true, "autoSearchPaths": true, "autoImportCompletions": true, "indexing": true, "reportMissingImports": "warning", "reportMissingTypeStubs": "none", "reportImportCycles": "none", "reportUnusedImport": "none", "reportUnusedClass": "none", "reportUnusedFunction": "none", "reportUnusedVariable": "none", "reportDuplicateImport": "warning", "reportOptionalSubscript": "none", "reportOptionalMemberAccess": "none", "reportOptionalCall": "none", "reportOptionalIterable": "none", "reportOptionalContextManager": "none", "reportOptionalOperand": "none", "reportTypedDictNotRequiredAccess": "none", "reportPrivateImportUsage": "none", "reportConstantRedefinition": "none", "reportIncompatibleMethodOverride": "none", "reportIncompatibleVariableOverride": "none", "reportOverlappingOverloads": "none", "reportUninitializedInstanceVariable": "none", "reportCallInDefaultInitializer": "none", "reportUnnecessaryIsInstance": "none", "reportUnnecessaryCast": "none", "reportUnnecessaryComparison": "none", "reportUnnecessaryContains": "none", "reportAssertAlwaysTrue": "none", "reportSelfClsParameterName": "none", "reportImplicitStringConcatenation": "none", "reportInvalidStringEscapeSequence": "none", "reportUnknownParameterType": "none", "reportUnknownArgumentType": "none", "reportUnknownLambdaType": "none", "reportUnknownVariableType": "none", "reportUnknownMemberType": "none", "reportMissingParameterType": "none", "reportMissingTypeArgument": "none", "reportInvalidTypeVarUse": "none", "reportUnnecessaryTypeIgnoreComment": "none", "reportMatchNotExhaustive": "none"}