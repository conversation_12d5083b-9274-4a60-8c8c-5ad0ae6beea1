[bandit]
# Bandit configuration file for GitHub Advanced Security

# Exclude directories from security scans
exclude_dirs = tests,venv,.venv,env,.env,__pycache__,custom_stubs,node_modules,build,dist,docs,docs_source,junit,bin,dev_tools,scripts,tool_templates

# Skip specific test IDs
# B101: Use of assert detected
# B311: Standard pseudo-random generators are not suitable for security/cryptographic purposes
skips = B101,B311

# Set the output format for GitHub Advanced Security
output_format = json

# Set the output file for GitHub Advanced Security
output_file = security-reports/bandit-results.json

# Set the severity level for GitHub Advanced Security
# Options: LOW, MEDIUM, HIGH
severity = LOW

# Set the confidence level for GitHub Advanced Security
# Options: LOW, MEDIUM, HIGH
confidence = LOW