input {
  # UDP input for logs sent by the LogstashHandler
  udp {
    port => 5000
    codec => "json"
    tags => ["udp"]
  }
  
  # TCP input for logs sent by the LogstashHandler
  tcp {
    port => 5000
    codec => "json"
    tags => ["tcp"]
  }
  
  # File input for logs collected by Filebeat
  beats {
    port => 5044
    tags => ["filebeat"]
  }
}

filter {
  # Parse timestamp field
  date {
    match => ["timestamp", "ISO8601"]
    target => "@timestamp"
    remove_field => ["timestamp"]
  }
  
  # Add host information
  mutate {
    add_field => {
      "[@metadata][host]" => "%{host}"
    }
  }
  
  # Add application information
  if [app] {
    mutate {
      add_field => {
        "[@metadata][app]" => "%{app}"
      }
    }
  }
  
  # Add log level information
  if [level] {
    mutate {
      add_field => {
        "[@metadata][level]" => "%{level}"
      }
    }
  }
  
  # Add logger information
  if [logger] {
    mutate {
      add_field => {
        "[@metadata][logger]" => "%{logger}"
      }
    }
  }
}

output {
  # Send logs to Elasticsearch
  elasticsearch {
    hosts => ["elasticsearch:9200"]
    index => "logs-%{+YYYY.MM.dd}"
    document_type => "_doc"
  }
  
  # Output to console for debugging (comment out in production)
  # stdout {
  #   codec => rubydebug
  # }
}
