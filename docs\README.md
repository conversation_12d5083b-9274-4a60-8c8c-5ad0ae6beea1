# pAIssive Income Framework Documentation

Welcome to the pAIssive Income Framework documentation. This documentation provides detailed information about each component of the framework, how they work together, and how to use them to develop and monetize niche AI tools.

## Table of Contents

1. [Overview](overview.md)
2. [Getting Started](getting-started.md)
3. [Project Structure](project-structure.md)
4. [Agent Team](agent-team.md)
5. [Niche Analysis](niche-analysis.md)
6. [AI Models](ai-models.md)
7. [Monetization](monetization.md)
8. [Marketing](marketing.md)
9. [UI](ui.md)
10. [Tool Templates](tool-templates.md)
11. [API Reference](api-reference.md)
12. [Common Utilities](common-utils-tooling.md)
13. [DevOps & CI/CD Workflow](devops-workflow.md)
    - [CI/CD Pipeline](ci_cd_pipeline.md)
    - [Python Tests Workflow](github_actions_test_workflow.md)
    - [Test Coverage Workflow](test-coverage-workflow.md)
    - [Workflow Fixes and Coverage Updates (PR #139)](workflow-fixes-pr139.md)
    - [Setup PNPM Workflow](ci_cd/setup-pnpm.md)
    - [Test Setup Script Workflow](ci_cd/test-setup-script.md)
    - [Documentation Check Workflow](documentation-check-workflow.md)
    - [Docker Compose in GitHub Actions](github-actions-docker-compose.md)
    - [Docker Compose Workflows](docker-compose-workflows.md)
    - [Docker Build and Push Action Update](docker-build-push-action-update.md)
14. [Code Quality](linting_configuration.md)
    - [Linting Configuration](linting_configuration.md)
    - [Type Checking](type-checking.md)
15. [Frontend Development](frontend/)
    - [Vitest Framework](frontend/vitest-framework.md)
    - [E2E Testing](frontend/e2e-testing.md)
16. [Integrations](integrations/)
    - [CrewAI + CopilotKit Integration](CrewAI_CopilotKit_Integration.md)
    - [Advanced Integration Examples](examples/CrewAI_CopilotKit_Advanced_Examples.md)
    - [ARTIST Experiments](artist-experiments.md)
17. [Security](security/)
    - [Security Overview](security.md)
    - [CodeQL Workflows](security/codeql_workflows.md)
    - [Security Scanning](security_scanning.md)
    - [Security Scan Guide](security_scan_guide.md)
18. [Troubleshooting](troubleshooting.md)
19. [FAQ](faq.md)
20. [Glossary](glossary.md)
21. [Contributing](contributing.md)
22. [Documentation Guide](documentation-guide.md)
23. [Dependency Updates](dependency-updates/README.md)
    - [PostCSS 8.4.32](dependency-updates/postcss-8.4.32.md)

## About the Framework

The pAIssive Income Framework is a comprehensive system for developing and monetizing niche AI agents to generate passive income through subscription-based software tools powered by local AI.

This framework provides a structured approach to creating specialized AI-powered software tools that solve specific problems for targeted user groups. By focusing on niche markets with specific needs, these tools can provide high value to users while generating recurring subscription revenue.

## License

[MIT License](../LICENSE)

---

**Feedback:**
If you spot an error or want to suggest an improvement to this documentation, please open a GitHub issue or see the [documentation guide](documentation-guide.md) for more on update and feedback processes.