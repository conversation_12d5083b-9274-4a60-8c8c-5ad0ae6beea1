#!/bin/bash
# Enhanced script to fix Docker Compose errors in GitHub Actions CI environments
# This script provides comprehensive diagnostics and fixes for common Docker Compose issues

# Enable error handling but don't exit immediately on error
set +e
# Enable command tracing for better debugging in GitHub Actions
set -x

# Set CI-specific variables
export CI=true
export GITHUB_ACTIONS=true
export DOCKER_BUILDKIT=1
export COMPOSE_DOCKER_CLI_BUILD=1

# Log with timestamp
log() {
  echo "[$(date '+%Y-%m-%d %H:%M:%S')] $1"
}

# Function to check Docker daemon status
check_docker_status() {
  log "Checking Docker daemon status..."
  if ! docker info >/dev/null 2>&1; then
    log "❌ Docker daemon is not responding"
    return 1
  fi
  log "✅ Docker daemon is running"
  return 0
}

# Function to determine Docker Compose command
get_compose_cmd() {
  if command -v docker compose >/dev/null 2>&1; then
    echo "docker compose"
  elif command -v docker-compose >/dev/null 2>&1; then
    echo "docker-compose"
  else
    log "❌ Docker Compose not found"
    return 1
  fi
}

# Function to check Docker Compose version
check_compose_version() {
  local compose_cmd="$1"
  log "Checking Docker Compose version..."

  # Get Docker Compose version
  local version
  if [ "$compose_cmd" = "docker compose" ]; then
    version=$(docker compose version --short 2>/dev/null || echo "unknown")
  else
    version=$(docker-compose --version | grep -oE '[0-9]+\.[0-9]+\.[0-9]+' || echo "unknown")
  fi

  log "Docker Compose version: $version"
  return 0
}

# Function to validate docker-compose.yml
validate_compose_file() {
  local compose_cmd="$1"
  log "Validating docker-compose.yml file..."

  if [ ! -f "docker-compose.yml" ]; then
    log "❌ docker-compose.yml file not found"
    # Print current directory contents for debugging
    log "Current directory contents:"
    ls -la
    return 1
  fi

  # Print the first few lines of the file for debugging
  log "First 10 lines of docker-compose.yml:"
  head -n 10 docker-compose.yml

  # Try to validate the file
  log "Attempting to validate docker-compose.yml with command: $compose_cmd config"
  if $compose_cmd config >/dev/null 2>&1; then
    log "✅ docker-compose.yml file is valid"
    return 0
  else
    log "❌ docker-compose.yml file has syntax errors"

    # Try to fix common issues
    log "Attempting to fix common issues..."

    # Fix indentation issues
    log "Fixing indentation issues..."
    sed -i 's/\t/  /g' docker-compose.yml

    # Fix line ending issues
    log "Fixing line ending issues..."
    # Simple replacement for dos2unix that should work everywhere
    sed -i 's/\r$//' docker-compose.yml

    # Check if fixes worked
    log "Checking if fixes worked..."
    if $compose_cmd config >/dev/null 2>&1; then
      log "✅ docker-compose.yml file fixed successfully"
      return 0
    else
      log "❌ Failed to fix docker-compose.yml file"
      # Print the error message for debugging
      log "Error message from $compose_cmd config:"
      $compose_cmd config
      return 1
    fi
  fi
}

# Function to check and fix Docker network issues
fix_network_issues() {
  local network_name="paissive-network"
  log "Checking Docker network '$network_name'..."

  # Check if network exists
  if docker network inspect "$network_name" >/dev/null 2>&1; then
    log "✅ Network '$network_name' exists"

    # Check if network is in a bad state
    if docker network inspect "$network_name" | grep -q "Error"; then
      log "⚠️ Network '$network_name' is in a bad state, recreating..."
      docker network rm "$network_name" >/dev/null 2>&1 || true
      sleep 2
      docker network create "$network_name" || {
        log "❌ Failed to recreate network '$network_name'"
        return 1
      }
    fi
  else
    log "Network '$network_name' does not exist, creating..."
    docker network create "$network_name" || {
      log "❌ Failed to create network '$network_name'"
      return 1
    }
  fi

  log "✅ Network '$network_name' is ready"
  return 0
}

# Function to check and fix Docker volume issues
fix_volume_issues() {
  log "Checking Docker volumes..."

  # List of volumes used in the project
  local volumes=("postgres-data" "frontend-node-modules" "redis-data")

  for volume in "${volumes[@]}"; do
    if docker volume inspect "$volume" >/dev/null 2>&1; then
      log "✅ Volume '$volume' exists"
    else
      log "Volume '$volume' does not exist, creating..."
      docker volume create "$volume" || {
        log "⚠️ Failed to create volume '$volume', but continuing..."
      }
    fi
  done

  log "✅ Docker volumes are ready"
  return 0
}

# Function to check and fix Docker image issues
fix_image_issues() {
  log "Checking Docker images..."

  # List of base images used in the project
  local images=("postgres:15.3-alpine" "node:18-alpine")

  for image in "${images[@]}"; do
    if docker image inspect "$image" >/dev/null 2>&1; then
      log "✅ Image '$image' exists"
    else
      log "Image '$image' does not exist, pulling..."
      if ! docker pull "$image"; then
        log "⚠️ Failed to pull image '$image', trying fallback images..."

        if [[ "$image" == "postgres:"* ]]; then
          # Try fallback PostgreSQL images
          for fallback_image in "postgres:14-alpine" "postgres:13-alpine" "postgres:alpine"; do
            log "Trying fallback image: $fallback_image"
            if docker pull "$fallback_image"; then
              log "✅ Successfully pulled fallback image: $fallback_image"

              # Update docker-compose.yml to use fallback image
              log "Updating docker-compose.yml to use fallback image..."
              sed -i "s|$image|$fallback_image|g" docker-compose.yml

              break
            fi
          done
        elif [[ "$image" == "node:"* ]]; then
          # Try fallback Node.js images
          for fallback_image in "node:16-alpine" "node:14-alpine"; do
            log "Trying fallback image: $fallback_image"
            if docker pull "$fallback_image"; then
              log "✅ Successfully pulled fallback image: $fallback_image"

              # Update Dockerfile.dev to use fallback image
              if [ -f "ui/react_frontend/Dockerfile.dev" ]; then
                log "Updating Dockerfile.dev to use fallback image..."
                sed -i "s|FROM $image|FROM $fallback_image|g" ui/react_frontend/Dockerfile.dev
              fi

              break
            fi
          done
        fi
      fi
    fi
  done

  log "✅ Docker images are ready"
  return 0
}

# Function to check and fix Docker Compose service issues
fix_service_issues() {
  local compose_cmd="$1"
  log "Checking Docker Compose services..."

  # Check if any services are already running
  if $compose_cmd ps | grep -q "Up"; then
    log "Some services are already running, stopping them..."
    $compose_cmd down -v || true
    sleep 5
  fi

  log "✅ Docker Compose services are ready to start"
  return 0
}

# Function to check system resources
check_system_resources() {
  log "Checking system resources..."

  # Check disk space
  local available_disk=$(df -m / | awk 'NR==2 {print $4}')
  log "Available disk space: ${available_disk}MB"

  if [ "$available_disk" -lt 1000 ]; then
    log "⚠️ Low disk space warning: less than 1GB available"
    log "Cleaning up Docker system to free space..."
    docker system prune -a -f --volumes || true
  fi

  # Check memory
  local available_mem=$(free -m | awk 'NR==2 {print $7}')
  log "Available memory: ${available_mem}MB"

  if [ "$available_mem" -lt 500 ]; then
    log "⚠️ Low memory warning: less than 500MB available"
  fi

  log "✅ System resources checked"
  return 0
}

# Main function
main() {
  log "Starting Docker Compose error fix script for GitHub Actions..."

  # Check Docker status
  if ! check_docker_status; then
    log "❌ Docker daemon is not responding. Cannot continue."
    exit 1
  fi

  # Get Docker Compose command
  local compose_cmd
  compose_cmd=$(get_compose_cmd)

  if [ -z "$compose_cmd" ]; then
    log "❌ Docker Compose not found. Cannot continue."
    exit 1
  fi

  log "Using Docker Compose command: $compose_cmd"

  # Check Docker Compose version
  check_compose_version "$compose_cmd"

  # Check system resources
  check_system_resources

  # Make sure scripts are executable
  log "Making scripts executable..."
  chmod +x docker-healthcheck.sh wait-for-db.sh || log "⚠️ Could not make scripts executable, but continuing..."

  # Validate docker-compose.yml file
  if ! validate_compose_file "$compose_cmd"; then
    log "⚠️ docker-compose.yml validation failed, but continuing..."
  fi

  # Fix Docker network issues
  if ! fix_network_issues; then
    log "⚠️ Failed to fix network issues, but continuing..."
  fi

  # Fix Docker volume issues
  if ! fix_volume_issues; then
    log "⚠️ Failed to fix volume issues, but continuing..."
  fi

  # Fix Docker image issues
  if ! fix_image_issues; then
    log "⚠️ Failed to fix image issues, but continuing..."
  fi

  # Fix Docker Compose service issues
  if ! fix_service_issues "$compose_cmd"; then
    log "⚠️ Failed to fix service issues, but continuing..."
  fi

  # Create necessary directories
  log "Creating necessary directories..."
  mkdir -p data logs
  chmod -R 777 data logs || log "⚠️ Could not fix permissions on data and logs directories, but continuing..."

  log "✅ Docker Compose error fix script completed successfully."
  return 0
}

# Run the main function
main
exit $?
