# React Frontend

This directory contains the React-based frontend for the project.

## CopilotKit + CrewAI Integration

We have integrated [CopilotKit + CrewAI](https://v0-crew-land.vercel.app/) to enable multi-agent AI features, agentic chat, and human-in-the-loop workflows in the UI.  
A demo Copilot agent chat is now available in the main app view.

---

## Getting Started

### 1. Install dependencies

If using **npm**:
```sh
npm install
```

Or, if using **pnpm** (recommended, since `pnpm-lock.yaml` is present):
```sh
pnpm install
```

### 2. Start the React app

If using **npm**:
```sh
npm start
```

Or, if using **pnpm**:
```sh
pnpm start
```

---

You should see the CopilotKit + CrewAI chat demo in the browser.

## Learn More

- [CopilotKit + CrewAI Docs](https://docs.copilotkit.ai/crewai-crews)
- [Integration Guide](./CopilotKit_CrewAI.md)