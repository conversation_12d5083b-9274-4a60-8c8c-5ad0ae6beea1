name: Consolidated CI/CD

# Consolidated CI/CD Pipeline
# This workflow handles continuous integration and deployment across multiple platforms.
#
# Jobs:
# - lint-test: Code quality, type checking, and testing
#   - Runs on: Ubuntu, Windows, MacOS
#   - Performs: linting (ruff), type checking (mypy), testing (pytest)
#   - Generates: test reports and coverage data
#
# - security: Comprehensive security scanning
#   - Runs on: Ubuntu, Windows, MacOS
#   - Tools: Safety, Bandit, Trivy, Semgrep, pip-audit, Gitleaks
#   - Generates: SARIF reports and security artifacts
#
# - build-deploy: Docker image building and publishing
#   - Runs on: Ubuntu only (for Docker compatibility)
#   - Triggers: On main/dev branch pushes and version tags
#   - Handles: Docker image building, caching, and publishing
#   - Uses: Docker Buildx for optimized builds

on:
  push:
    branches: [ main, dev, master, develop ]
    tags:
      - 'v*.*.*'
  pull_request:
    branches: [ main, dev, master, develop ]
  schedule:
    - cron: '0 0 * * 0'  # Weekly, for regular security scans
  workflow_dispatch:

permissions:
  contents: read

jobs:
  lint-test:
    name: <PERSON><PERSON>, Type Check, and Test
    runs-on: ${{ matrix.os }}
    timeout-minutes: 30
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
      fail-fast: false
    permissions:
      contents: read
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: "3.12"

      - name: Cache uv dependencies
        uses: actions/cache@v4
        with:
          path: |
            ~/.cache/uv
            ~/.uv
          key: ${{ runner.os }}-uv-${{ hashFiles('**/requirements*.txt') }}
          restore-keys: |
            ${{ runner.os }}-uv-

      - name: Install uv (Unix)
        if: runner.os != 'Windows'
        run: |
          curl -LsSf https://astral.sh/uv/install.sh | sh
          echo "$HOME/.cargo/bin" >> $GITHUB_PATH
          # Verify uv is installed and in PATH
          which uv || echo "uv not found in PATH"

      - name: Install uv (Windows)
        if: runner.os == 'Windows'
        run: |
          iwr -useb https://astral.sh/uv/install.ps1 | iex
          echo "$HOME\.cargo\bin" | Out-File -FilePath $env:GITHUB_PATH -Encoding utf8 -Append
        shell: pwsh

      - name: Install dependencies (Unix)
        if: runner.os != 'Windows'
        run: |
          # Ensure pip is up to date
          python -m pip install --upgrade pip

          # Install uv if not already available
          which uv || python -m pip install uv

          # Install testing tools
          python -m pip install ruff mypy pytest pytest-cov pytest-xdist pytest-asyncio

          # Install requirements
          if [ -f requirements-dev.txt ]; then python -m pip install -r requirements-dev.txt; fi
          if [ -f requirements.txt ]; then python -m pip install -r requirements.txt; fi

          # Install MCP SDK using the installation script
          echo "Installing MCP SDK using installation script..."
          python install_mcp_sdk.py

      - name: Install dependencies (Windows)
        if: runner.os == 'Windows'
        shell: pwsh
        run: |
          # Ensure pip is up to date
          python -m pip install --upgrade pip

          # Install testing tools
          python -m pip install ruff mypy pytest pytest-cov pytest-xdist pytest-asyncio

          # Install requirements (excluding MCP-related packages)
          if (Test-Path requirements-dev.txt) {
            python -m pip install -r requirements-dev.txt
          }

          # Install requirements.txt but skip MCP packages
          if (Test-Path requirements.txt) {
            $requirements = Get-Content requirements.txt | Where-Object { -not $_.Contains("mcp") -and -not $_.Contains("modelcontextprotocol") }
            $requirements | Set-Content -Path "requirements_filtered.txt"
            python -m pip install -r requirements_filtered.txt
          }

          # Create mock MCP module for Windows
          python install_mcp_sdk.py

      - name: Run linting (Unix)
        if: runner.os != 'Windows'
        run: |
          ruff check .
          mypy .

      - name: Run linting (Windows)
        if: runner.os == 'Windows'
        shell: pwsh
        run: |
          # Skip MCP adapter files during linting on Windows
          ruff check --exclude "ai_models/adapters/mcp_adapter.py" --exclude "tests/ai_models/adapters/test_mcp_adapter.py" --exclude "tests/test_mcp_import.py" --exclude "tests/test_mcp_top_level_import.py" .
          mypy --exclude "ai_models/adapters/mcp_adapter.py" --exclude "tests/ai_models/adapters/test_mcp_adapter.py" --exclude "tests/test_mcp_import.py" --exclude "tests/test_mcp_top_level_import.py" .

      - name: Run MCP tests (Unix only)
        if: runner.os != 'Windows'
        run: |
          # Run MCP adapter tests separately using the custom script
          python run_mcp_tests.py

      - name: Run other tests
        run: |
          # Run all tests except MCP adapter tests
          pytest -v --cov=. --cov-report=xml --cov-report=term-missing --ignore=tests/ai_models/adapters/test_mcp_adapter.py --ignore=tests/test_mcp_import.py --ignore=tests/test_mcp_top_level_import.py

      - name: Upload coverage to Codecov
        uses: codecov/codecov-action@v3
        with:
          file: ./coverage.xml

  security:
    name: Security & SAST
    runs-on: ${{ matrix.os }}
    timeout-minutes: 25
    strategy:
      matrix:
        os: [ubuntu-latest, windows-latest, macos-latest]
      fail-fast: false
    permissions:
      security-events: write
      contents: read
      actions: read
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Create security reports directory
        run: mkdir -p security-reports
        shell: bash

      - name: Install security tools (Unix)
        if: runner.os != 'Windows'
        run: |
          python -m pip install --upgrade pip
          pip install safety bandit semgrep pip-audit

      - name: Install security tools (Windows)
        if: runner.os == 'Windows'
        shell: pwsh
        run: |
          python -m pip install --upgrade pip
          pip install safety bandit semgrep pip-audit

      - name: Run security scans (Unix)
        if: runner.os != 'Windows'
        continue-on-error: true
        run: |
          # Create security-reports directory if it doesn't exist
          mkdir -p security-reports
          
          # Create .github/bandit directory if it doesn't exist
          mkdir -p .github/bandit
          
          # Generate Bandit configuration files
          python generate_bandit_config.py ${{ github.run_id }}
          
          # Run safety check
          safety check || true
          
          # Run Bandit with the generated configuration
          platform=$(echo ${{ runner.os }} | tr '[:upper:]' '[:lower:]')
          bandit_config_file=".github/bandit/bandit-config-${platform}-${{ github.run_id }}.yaml"
          if [ -f "$bandit_config_file" ]; then
            echo "Using Bandit configuration file: $bandit_config_file"
            bandit -r . -f sarif -o security-reports/bandit-results.sarif -c "$bandit_config_file" --exclude ".venv,node_modules,tests" || true
          else
            echo "Bandit configuration file not found. Using default configuration."
            bandit -r . -f sarif -o security-reports/bandit-results.sarif --exclude ".venv,node_modules,tests" || true
          fi
          
          # Ensure the SARIF file exists
          if [ ! -f "security-reports/bandit-results.sarif" ]; then
            echo "Bandit did not generate a SARIF file. Creating an empty one."
            cat > security-reports/bandit-results.sarif << 'EOF'
{
  "version": "2.1.0",
  "$schema": "https://raw.githubusercontent.com/oasis-tcs/sarif-spec/master/Schemata/sarif-schema-2.1.0.json",
  "runs": [
    {
      "tool": {
        "driver": {
          "name": "Bandit",
          "informationUri": "https://github.com/PyCQA/bandit",
          "version": "1.7.5",
          "rules": []
        }
      },
      "results": []
    }
  ]
}
EOF
          fi
          
          # Run pip-audit
          pip-audit || true
          
          # Run semgrep
          semgrep scan --config auto || true

      - name: Run security scans (Windows)
        if: runner.os == 'Windows'
        continue-on-error: true
        shell: pwsh
        run: |
          # Create security-reports directory if it doesn't exist
          New-Item -ItemType Directory -Force -Path security-reports
          
          # Create .github/bandit directory if it doesn't exist
          New-Item -ItemType Directory -Force -Path .github/bandit
          
          # Generate Bandit configuration files
          python generate_bandit_config.py ${{ github.run_id }}
          
          # Run safety check
          safety check
          
          # Run Bandit with the generated configuration
          $banditConfigFile = ".github/bandit/bandit-config-windows-${{ github.run_id }}.yaml"
          if (Test-Path $banditConfigFile) {
            Write-Host "Using Bandit configuration file: $banditConfigFile"
            bandit -r . -f sarif -o security-reports/bandit-results.sarif -c $banditConfigFile --exclude ".venv,node_modules,tests"
          } else {
            Write-Host "Bandit configuration file not found. Using default configuration."
            bandit -r . -f sarif -o security-reports/bandit-results.sarif --exclude ".venv,node_modules,tests"
          }
          
          # Ensure the SARIF file exists
          if (-not (Test-Path "security-reports/bandit-results.sarif")) {
            Write-Host "Bandit did not generate a SARIF file. Creating an empty one."
            $emptySarif = @"
{
  "version": "2.1.0",
  "$schema": "https://raw.githubusercontent.com/oasis-tcs/sarif-spec/master/Schemata/sarif-schema-2.1.0.json",
  "runs": [
    {
      "tool": {
        "driver": {
          "name": "Bandit",
          "informationUri": "https://github.com/PyCQA/bandit",
          "version": "1.7.5",
          "rules": []
        }
      },
      "results": []
    }
  ]
}
"@
            Set-Content -Path "security-reports/bandit-results.sarif" -Value $emptySarif
          }
          
          # Run pip-audit
          pip-audit
          
          # Run semgrep
          semgrep scan --config auto

      - name: Run Trivy vulnerability scanner
        uses: aquasecurity/trivy-action@master
        continue-on-error: true
        with:
          scan-type: 'fs'
          scan-ref: '.'
          format: 'sarif'
          output: 'security-reports/trivy-results.sarif'

      - name: Upload security reports
        uses: actions/upload-artifact@v4
        if: always()
        with:
          name: security-reports-${{ runner.os }}-${{ github.run_id }}
          path: security-reports/
          retention-days: 7

  build-deploy:
    name: Build & Deploy
    runs-on: ubuntu-latest
    needs: [lint-test, security]
    if: |
      (github.event_name == 'push' && (github.ref == 'refs/heads/main' || github.ref == 'refs/heads/dev' || github.ref == 'refs/heads/master' || github.ref == 'refs/heads/develop')) ||
      github.event_name == 'workflow_dispatch' ||
      startsWith(github.ref, 'refs/tags/v')
    permissions:
      contents: read
      packages: write
      id-token: write
    outputs:
      docker_tag: ${{ steps.set-docker-tag.outputs.docker_tag }}
      should_push: ${{ steps.set-docker-tag.outputs.should_push }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set Docker image tag
        id: set-docker-tag
        run: |
          if [[ "${{ github.ref }}" == refs/tags/v* ]]; then
            echo "docker_tag=${{ secrets.DOCKERHUB_USERNAME }}/paissiveincome-app:${{ github.ref_name }}" >> $GITHUB_OUTPUT
            echo "should_push=true" >> $GITHUB_OUTPUT
          else
            echo "docker_tag=paissiveincome/app:test" >> $GITHUB_OUTPUT
            echo "should_push=false" >> $GITHUB_OUTPUT
          fi

      - name: Set up QEMU
        uses: docker/setup-qemu-action@v3
        with:
          platforms: 'arm64,amd64'

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          platforms: linux/amd64,linux/arm64
          driver-opts: |
            image=moby/buildkit:v0.12.0

      - name: Log in to Docker Hub
        if: steps.set-docker-tag.outputs.should_push == 'true'
        uses: docker/login-action@v3
        with:
          username: ${{ secrets.DOCKERHUB_USERNAME }}
          password: ${{ secrets.DOCKERHUB_TOKEN }}

      - name: Prepare build cache
        uses: actions/cache@v4
        with:
          path: /tmp/.buildx-cache
          key: ${{ runner.os }}-buildx-${{ github.sha }}
          restore-keys: |
            ${{ runner.os }}-buildx-

      - name: Build and push Docker image
        uses: docker/build-push-action@v5
        with:
          context: .
          push: ${{ steps.set-docker-tag.outputs.should_push }}
          tags: ${{ steps.set-docker-tag.outputs.docker_tag }}
          platforms: linux/amd64,linux/arm64
          cache-from: type=local,src=/tmp/.buildx-cache
          cache-to: type=local,dest=/tmp/.buildx-cache-new,mode=max
          build-args: |
            BUILDKIT_INLINE_CACHE=1
          provenance: mode=max

      - name: Move Docker cache
        run: |
          rm -rf /tmp/.buildx-cache
          mv /tmp/.buildx-cache-new /tmp/.buildx-cache