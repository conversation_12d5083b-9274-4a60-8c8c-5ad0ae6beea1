# Core dependencies
fastapi>=0.95.0
uvicorn>=0.22.0
pydantic>=2.0.0
redis>=4.5.0
python-multipart>=0.0.9
httpx>=0.24.0
flask>=2.0.1
celery>=5.2.0
flask-socketio>=5.1.0
pyjwt>=2.6.0
bcrypt>=4.0.0
pyperclip>=1.8.2  # For secure clipboard operations
cryptography>=41.0.0  # For encryption of sensitive files

# MCP integration
modelcontextprotocol>=0.1.0  # Required for MCP server integration
mcp-use>=0.1.0  # Required for MCP server integration

# Service dependencies
grpcio>=1.54.0
grpcio-reflection>=1.54.0
grpcio-health-checking>=1.54.0
protobuf>=3.19,<5.0

# AI and ML dependencies
torch>=1.10.0
transformers>=4.20.0
sentence-transformers>=2.2.0
numpy>=1.24.3
scikit-learn>=1.0.0

# Utility dependencies
requests>=2.25.0
psutil>=5.9.5
tqdm>=4.65.0
python-dateutil>=2.8.2
jinja2>=3.0.0
prometheus_client>=0.16.0

# Visualization and analytics
matplotlib>=3.5.0
pandas>=1.5.0
plotly>=5.13.0

# Database and ORM
psycopg2-binary>=2.9.0
SQLAlchemy>=2.0.0
Flask-SQLAlchemy>=3.0.0
Flask-Migrate>=4.0.0

# Development and testing
pytest>=7.0.0
pytest-cov>=4.0.0
pytest-mock>=3.10.0
pytest-asyncio>=0.21.0
aiohttp>=3.8.0
aio_pika>=9.0.0
