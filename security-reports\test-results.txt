Run started:2025-05-18 04:01:21.792497

Test results:
>> Issue: [B105:hardcoded_password_string] Possible hardcoded password: 'DEMO_TOKEN_PLACEHOLDER'
   Severity: Low   Confidence: Medium
   CWE: CWE-259 (https://cwe.mitre.org/data/definitions/259.html)
   More Info: https://bandit.readthedocs.io/en/1.8.3/plugins/b105_hardcoded_password_string.html
   Location: .\common_utils\logging\examples.py:28:24
27	    # Using placeholder values for demonstration only
28	    demo_access_token = "DEMO_TOKEN_PLACEHOLDER"  # noqa: S105
29	    demo_auth_material = "DEMO_AUTH_PLACEHOLDER"

--------------------------------------------------
>> Issue: [B105:hardcoded_password_string] Possible hardcoded password: 'DEMO_TOKEN_PLACEHOLDER'
   Severity: Low   Confidence: Medium
   CWE: CWE-259 (https://cwe.mitre.org/data/definitions/259.html)
   More Info: https://bandit.readthedocs.io/en/1.8.3/plugins/b105_hardcoded_password_string.html
   Location: .\common_utils\logging\examples.py:55:24
54	    # Sensitive data - using placeholder for demonstration
55	    demo_access_token = "DEMO_TOKEN_PLACEHOLDER"  # noqa: S105
56	

--------------------------------------------------
>> Issue: [B105:hardcoded_password_string] Possible hardcoded password: 'secret:'
   Severity: Low   Confidence: Medium
   CWE: CWE-259 (https://cwe.mitre.org/data/definitions/259.html)
   More Info: https://bandit.readthedocs.io/en/1.8.3/plugins/b105_hardcoded_password_string.html
   Location: .\common_utils\secrets\config.py:29:20
28	    # Constants
29	    SECRET_PREFIX = "secret:"  # noqa: S105
30	

--------------------------------------------------
>> Issue: [B404:blacklist] Consider possible security implications associated with the subprocess module.
   Severity: Low   Confidence: High
   CWE: CWE-78 (https://cwe.mitre.org/data/definitions/78.html)
   More Info: https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b404-import-subprocess
   Location: .\fix_linting_issues.py:16:0
15	import shutil
16	import subprocess
17	import sys

--------------------------------------------------
>> Issue: [B603:subprocess_without_shell_equals_true] subprocess call - check for execution of untrusted input.
   Severity: Low   Confidence: High
   CWE: CWE-78 (https://cwe.mitre.org/data/definitions/78.html)
   More Info: https://bandit.readthedocs.io/en/1.8.3/plugins/b603_subprocess_without_shell_equals_true.html
   Location: .\fix_linting_issues.py:63:17
62	    try:
63	        result = subprocess.run(  # noqa: S603 - Using full path to git executable
64	            [git_exe, "rev-parse", "--show-toplevel"],
65	            capture_output=True,
66	            text=True,
67	            check=True,
68	        )
69	        return Path(result.stdout.strip())

--------------------------------------------------
>> Issue: [B603:subprocess_without_shell_equals_true] subprocess call - check for execution of untrusted input.
   Severity: Low   Confidence: High
   CWE: CWE-78 (https://cwe.mitre.org/data/definitions/78.html)
   More Info: https://bandit.readthedocs.io/en/1.8.3/plugins/b603_subprocess_without_shell_equals_true.html
   Location: .\fix_linting_issues.py:222:21
221	            full_cmd = [ruff_path] + ruff_cmd[1:]
222	            result = subprocess.run(  # noqa: S603 - Using full path to ruff executable
223	                full_cmd,
224	                capture_output=True,
225	                text=True,
226	                check=False,
227	            )
228	

--------------------------------------------------
>> Issue: [B603:subprocess_without_shell_equals_true] subprocess call - check for execution of untrusted input.
   Severity: Low   Confidence: High
   CWE: CWE-78 (https://cwe.mitre.org/data/definitions/78.html)
   More Info: https://bandit.readthedocs.io/en/1.8.3/plugins/b603_subprocess_without_shell_equals_true.html
   Location: .\fix_linting_issues.py:239:32
238	                ruff_path = get_executable_path("ruff")
239	                format_result = subprocess.run(  # noqa: S603 - Using full path to ruff executable
240	                    [ruff_path, "format", str(file_path)],
241	                    capture_output=True,
242	                    text=True,
243	                    check=False,
244	                )
245	

--------------------------------------------------
>> Issue: [B105:hardcoded_password_string] Possible hardcoded password: ''
   Severity: Low   Confidence: Medium
   CWE: CWE-259 (https://cwe.mitre.org/data/definitions/259.html)
   More Info: https://bandit.readthedocs.io/en/1.8.3/plugins/b105_hardcoded_password_string.html
   Location: .\init_db.py:39:15
38	    # Use a more secure method for random selection
39	    password = ""
40	    for _ in range(length):

--------------------------------------------------
>> Issue: [B404:blacklist] Consider possible security implications associated with the subprocess module.
   Severity: Low   Confidence: High
   CWE: CWE-78 (https://cwe.mitre.org/data/definitions/78.html)
   More Info: https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b404-import-subprocess
   Location: .\main_health_check.py:25:0
24	import shutil
25	import subprocess
26	import sys

--------------------------------------------------
>> Issue: [B603:subprocess_without_shell_equals_true] subprocess call - check for execution of untrusted input.
   Severity: Low   Confidence: High
   CWE: CWE-78 (https://cwe.mitre.org/data/definitions/78.html)
   More Info: https://bandit.readthedocs.io/en/1.8.3/plugins/b603_subprocess_without_shell_equals_true.html
   Location: .\main_health_check.py:57:10
56	    res = subprocess.run(  # noqa: S603
57	        cmd_list, shell=False, check=False, capture_output=True, text=True
58	    )
59	    if res.returncode != 0:
60	        logger.error("FAILED: %s", desc)

--------------------------------------------------
>> Issue: [B404:blacklist] Consider possible security implications associated with the subprocess module.
   Severity: Low   Confidence: High
   CWE: CWE-78 (https://cwe.mitre.org/data/definitions/78.html)
   More Info: https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b404-import-subprocess
   Location: .\run_pre_commit.py:14:0
13	import os
14	import subprocess
15	import sys

--------------------------------------------------
>> Issue: [B404:blacklist] Consider possible security implications associated with the subprocess module.
   Severity: Low   Confidence: High
   CWE: CWE-78 (https://cwe.mitre.org/data/definitions/78.html)
   More Info: https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b404-import-subprocess
   Location: .\run_pre_commit_on_all_files.py:9:0
8	import os
9	import subprocess
10	import sys

--------------------------------------------------
>> Issue: [B104:hardcoded_bind_all_interfaces] Possible binding to all interfaces.
   Severity: Medium   Confidence: Medium
   CWE: CWE-605 (https://cwe.mitre.org/data/definitions/605.html)
   More Info: https://bandit.readthedocs.io/en/1.8.3/plugins/b104_hardcoded_bind_all_interfaces.html
   Location: .\run_ui.py:312:23
311	    if is_container:
312	        default_host = "0.0.0.0"  # noqa: S104 - Intentional for container environments
313	        if hasattr(app, "logger"):

--------------------------------------------------
>> Issue: [B404:blacklist] Consider possible security implications associated with the subprocess module.
   Severity: Low   Confidence: High
   CWE: CWE-78 (https://cwe.mitre.org/data/definitions/78.html)
   More Info: https://bandit.readthedocs.io/en/1.8.3/blacklists/blacklist_imports.html#b404-import-subprocess
   Location: .\test_security_scan.py:15:0
14	import shlex
15	import subprocess
16	import sys

--------------------------------------------------
>> Issue: [B603:subprocess_without_shell_equals_true] subprocess call - check for execution of untrusted input.
   Severity: Low   Confidence: High
   CWE: CWE-78 (https://cwe.mitre.org/data/definitions/78.html)
   More Info: https://bandit.readthedocs.io/en/1.8.3/plugins/b603_subprocess_without_shell_equals_true.html
   Location: .\test_security_scan.py:42:17
41	            args,
42	            shell=False,  # Avoid shell=True for security
43	            capture_output=True,  # Use capture_output instead of stdout/stderr=PIPE
44	            cwd=cwd,
45	            text=True,
46	            check=False,
47	        )
48	        stdout, stderr, returncode = result.stdout, result.stderr, result.returncode
49	    except (subprocess.SubprocessError, OSError) as e:
50	        stdout, stderr, returncode = "", str(e), 1

--------------------------------------------------

Code scanned:
	Total lines of code: 10641
	Total lines skipped (#nosec): 0
	Total potential issues skipped due to specifically being disabled (e.g., #nosec BXXX): 12

Run metrics:
	Total issues (by severity):
		Undefined: 0
		Low: 14
		Medium: 1
		High: 0
	Total issues (by confidence):
		Undefined: 0
		Low: 0
		Medium: 5
		High: 10
Files skipped (0):
