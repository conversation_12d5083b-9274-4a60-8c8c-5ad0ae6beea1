# JavaScript Source Code

This directory contains JavaScript source code for the project.

## Requirements

- Node.js 18 or higher (required for nyc 17.1.0)
- pnpm (preferred package manager)

## Testing

Tests are written using Mocha and can be run with:

```bash
pnpm test
```

Coverage reports are generated using nyc (Istanbul) and can be viewed with:

```bash
pnpm coverage
```

## Files

- `math.js`: Simple math utility functions for demonstration
- `math.test.js`: Tests for the math utility functions
